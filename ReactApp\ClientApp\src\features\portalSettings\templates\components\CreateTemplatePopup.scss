// Create Template Popup Styles
.create-template-popup {
  .k-dialog-titlebar {
    background-color: #007acc; // Blue header background
    color: white;
    border-bottom: 1px solid #005a99;
    
    .k-dialog-title {
      color: white;
      font-weight: 600;
      font-size: 16px;
    }
    
    .k-dialog-close {
      color: white;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .k-dialog-content {
    padding: 0;
    overflow: hidden;
  }

  .popup-content {
    display: flex;
    height: 400px; // Fixed height for consistent layout
    
    .popup-left {
      flex: 1;
      padding: 24px;
      border-right: 1px solid #e0e0e0; // Border separation
      background-color: #fafafa;
      overflow-y: auto;
    }
    
    .popup-right {
      flex: 1;
      padding: 24px;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .preview-placeholder {
        text-align: center;
        color: #666;
        font-style: italic;
      }
    }
  }

  .k-dialog-actions {
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    
    .k-button {
      margin-left: 8px;
      
      &:first-child {
        margin-left: 0;
      }
    }
  }
}

// Form Styles
.create-template-form {
  .form-group {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }
  
  .k-textbox {
    width: 100%;
    
    &.k-invalid {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }
  
  .validation-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
    display: block;
  }
  
  .switch-group {
    .switch-container {
      display: flex;
      align-items: center;
      
      .k-switch {
        margin-right: 12px;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .create-template-popup {
    width: 95% !important;
    height: auto !important;
    
    .popup-content {
      flex-direction: column;
      height: auto;
      
      .popup-left {
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
      }
      
      .popup-right {
        min-height: 150px;
      }
    }
  }
}

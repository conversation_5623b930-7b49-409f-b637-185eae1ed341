import { useTranslation } from "react-i18next";
import "./Templates.scss";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { Button } from "@progress/kendo-react-buttons";

export default function TemplatesPage() {
  const { t } = useTranslation("dashboard");

  return (
    <SectionLayout
      headerActions={
        <Button
          size="small"
          className="header-action-btn"
          icon="add"
          themeColor="base"
        >
          {t("btn.createTemplate")}
        </Button>
      }
    >
      <div>Templates Grid View Comes here</div>
    </SectionLayout>
  );
}
